/*
Theme Name: DmrThema
Author: Cline
Description: DmrThem<PERSON> i<PERSON><PERSON> o<PERSON>ak geliştirilmiş bir te<PERSON>.
Version: 1.0
*/

html {
    height: 100%;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1 0 auto;
}

.container {
    width: 75%;
    margin: 0 auto;
}

/* Header Stilleri */
.site-header {
    border-bottom: 1px solid #e0e0e0;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
}

.logo a {
    text-decoration: none;
    color: #ff6000;
    font-size: 28px;
    font-weight: bold;
}

.search-form-container {
    flex-grow: 1;
    margin: 0 30px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-form {
    display: flex;
    width: 100%;
}

.search-form label {
    flex-grow: 1;
}

.search-field {
    width: 100%;
    padding: 15px;
    border: 1px solid #ccc;
    border-right: none;
    border-radius: 5px 0 0 5px;
}

.search-submit {
    padding: 15px 20px;
    border: 1px solid #ff6000;
    background-color: #ff6000;
    color: white;
    cursor: pointer;
    border-radius: 0 5px 5px 0;
}

.user-actions a, .location, .cart a {
    text-decoration: none;
    color: #333;
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.user-actions a span {
    font-size: 12px;
    color: #666;
    display: block;
    margin-top: 2px;
}

.cart a {
    background-color: #f0f0f0;
}

.header-bottom .cart a {
    padding: 10px 15px; /* Bu padding değeri menü ile aynı hizada olmasını sağlar, gerekirse ayarlanabilir */
}

.header-bottom {
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

.header-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}

.main-navigation ul {
    display: flex;
    justify-content: flex-start; /* Elemanları sola hizalar */
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation ul li {
    margin-right: 20px;
}

.main-navigation ul li a {
    display: block;
    padding: 20px 15px;
    text-decoration: none;
    color: #333;
    font-weight: bold;
}

.main-navigation ul li a:hover {
    background-color: #e0e0e0;
}

/* Mega Menü Stilleri */
.main-navigation ul li.has-mega-menu {
    position: static; /* Konumlandırmayı header'a göre yapmak için */
}

.main-navigation ul .sub-menu {
    display: none;
    position: absolute;
    left: 0; /* Tam genişlik için */
    right: 0; /* Tam genişlik için */
    width: auto; /* Sol ve sağa göre otomatik genişlik */
    top: auto; /* Üst konumu header'a göre ayarlar */
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 20px 0;
    z-index: 500; /* Slider'ın üstünde görünmesi için */
}

.main-navigation ul li:hover > .sub-menu {
    display: block;
}

.sub-menu .sub-menu-container {
    width: 75%; /* .container ile aynı genişlik */
    margin: 0 auto;
    display: block; /* Flex yerine block */
}

.sub-menu .sub-menu-container > li {
    width: 100%; /* Tek sütun için tam genişlik */
    padding-right: 0;
    box-sizing: border-box;
}

.sub-menu li {
    width: 100%;
}

.sub-menu ul li a {
    padding: 8px 0 !important;
    font-weight: normal !important;
    color: #666 !important;
}

.sub-menu ul li a:hover {
    color: #ff6000 !important;
    background-color: transparent !important;
}

.sub-menu .sub-menu-container > li > a {
    font-weight: bold !important;
    color: #333 !important;
}

.sub-menu .sub-menu-container > li ul {
    list-style: none;
    padding-left: 0;
}

/* Renkli Şerit */
.header-bottom::before {
    content: '';
    display: block;
    height: 4px;
    background: linear-gradient(to right, #ff6000 20%, #41b6e6 20%, #41b6e6 40%, #8a3ffc 40%, #8a3ffc 60%, #4caf50 60%, #4caf50 80%, #5e35b1 80%);
}

/* Footer Stilleri */
.site-footer {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 40px 0;
    margin-top: 40px;
}

.footer-widgets {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.widget-area {
    width: 30%;
}

.widget-area h3 {
    color: #fff;
    border-bottom: 2px solid #ff6000;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.widget-area ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget-area ul li a {
    color: #ecf0f1;
    text-decoration: none;
    line-height: 2;
    transition: color 0.3s;
}

.widget-area ul li a:hover {
    color: #ff6000;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #34495e;
    padding-top: 20px;
}

.footer-bottom p {
    margin: 0;
    color: #bdc3c7;
}

/* Slider Stilleri */
.main-slider {
    width: 100%;
    height: 500px; /* Yüksekliği isteğe göre ayarlayabilirsiniz */
    margin-bottom: 40px;
    margin-top: 0; /* Header ile boşluğu kaldırmak için */
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* WooCommerce Temel Stiller */
.woocommerce .content-area,
.woocommerce-page .content-area {
    width: 100%;
}

.woocommerce .site-main,
.woocommerce-page .site-main {
    margin: 0;
}

/* Ürün Detay Sayfası */
.product-details-wrapper {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.product .images {
    flex: 1;
}

.product .summary {
    flex: 1;
}

@media (max-width: 768px) {
    .product-details-wrapper {
        flex-direction: column;
        gap: 20px;
    }
}
