/*
WooCommerce Styles for DmrThema
========
*/

/* Basic WooCommerce Layout */
.woocommerce .content-area {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.woocommerce .site-main {
    padding: 20px 0;
}

.shop-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.woocommerce-breadcrumb {
    padding: 1rem 0;
    margin-bottom: 2rem;
    font-size: 14px;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 5px;
}

.woocommerce-breadcrumb a {
    color: #333;
    text-decoration: none;
}

.woocommerce-breadcrumb a:hover {
    color: #ff6000;
    text-decoration: underline;
}

/* Shop Header */
.woocommerce-products-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #f0f0f0;
}

.woocommerce-products-header__title {
    font-size: 2.5em;
    color: #333;
    margin: 0;
    font-weight: 600;
}

/* Product Grid */
ul.products {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 0;
    list-style: none;
    padding: 0;
}

ul.products li.product {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    margin: 0;
    padding: 0;
}

ul.products li.product:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #ff6000;
}

/* Product Image Container */
.woocommerce-image__wrapper {
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

ul.products li.product img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

ul.products li.product:hover img {
    transform: scale(1.1);
}

/* Product Card Structure */
.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Product Content */
.product-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.woocommerce-loop-product__title {
    font-size: 18px;
    margin: 0 0 10px 0;
    line-height: 1.4;
    height: 50px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.woocommerce-loop-product__title a {
    color: #333;
    text-decoration: none;
    font-weight: 600;
}

.woocommerce-loop-product__title a:hover {
    color: #ff6000;
}

/* Star Rating */
.star-rating {
    font-size: 14px;
    color: #ff6000;
    margin-bottom: 10px;
    display: block;
}

.price {
    font-size: 20px;
    font-weight: bold;
    color: #ff6000;
    margin-bottom: 15px;
    display: block;
}

.price del {
    color: #999;
    font-weight: normal;
    font-size: 16px;
    margin-right: 10px;
}

.price ins {
    text-decoration: none;
}

/* Add to Cart Button */
.button, .add_to_cart_button {
    background: #ff6000;
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    text-decoration: none;
    display: block;
    width: 100%;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.button:hover, .add_to_cart_button:hover {
    background: #e55500;
    color: white;
    transform: translateY(-2px);
}

/* Sale Badge */
.onsale {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #e74c3c;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Shop Controls */
.dmrthema-sorting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 15px;
}

.woocommerce-ordering {
    margin: 0;
}

.woocommerce-ordering select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    font-size: 14px;
}

.woocommerce-result-count {
    margin: 0;
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* Pagination */
.woocommerce-pagination {
    text-align: center;
    margin-top: 30px;
}

.woocommerce-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: #333;
    text-decoration: none;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.woocommerce-pagination .page-numbers:hover,
.woocommerce-pagination .page-numbers.current {
    background: #ff6000;
    color: white;
    border-color: #ff6000;
}

/* Messages */
.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.woocommerce-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.woocommerce-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.woocommerce-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Cart */
.cart-contents {
    color: #333;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
}

.cart-contents:hover {
    color: #ff6000;
}

.cart-contents .count {
    background: #ff6000;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    min-width: 18px;
    text-align: center;
}

/* Product Categories */
ul.products li.product.product-category h2 {
    text-align: center;
    margin-top: 15px;
}

ul.products li.product.product-category a {
    display: block;
    text-decoration: none;
    color: #333;
}

ul.products li.product.product-category a:hover {
    color: #ff6000;
}

/* Star Rating */
.star-rating {
    font-size: 14px;
    color: #ff6000;
    margin-bottom: 5px;
}

/* Out of Stock */
.product-out-of-stock {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

/* Responsive Design */
@media (max-width: 1024px) {
    ul.products {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .woocommerce .content-area {
        padding: 0 15px;
    }
}

@media (max-width: 768px) {
    ul.products {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 15px;
    }

    .dmrthema-sorting {
        flex-direction: column;
        text-align: center;
    }

    .woocommerce-products-header__title {
        font-size: 2em;
    }

    .woocommerce-image__wrapper {
        height: 200px;
    }
}

@media (max-width: 480px) {
    ul.products {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .woocommerce .content-area {
        padding: 0 10px;
    }

    .woocommerce-products-header__title {
        font-size: 1.8em;
    }

    .product-content {
        padding: 15px;
    }

    .woocommerce-image__wrapper {
        height: 250px;
    }
}

/* Additional Product Styling */
.woocommerce-loop-product__link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.woocommerce-loop-product__link:hover {
    text-decoration: none;
    color: inherit;
}

/* Product Info Layout */
.product-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

/* Loading and No Products */
.woocommerce-no-products-found {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 30px 0;
}

.woocommerce-no-products-found .woocommerce-info {
    font-size: 18px;
    color: #666;
    margin: 0;
    background: transparent;
    border: none;
    padding: 0;
}

/* Product Categories in Grid */
ul.products li.product.product-category {
    background: linear-gradient(135deg, #ff6000, #e55500);
    color: white;
    text-align: center;
}

ul.products li.product.product-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 96, 0, 0.3);
}

ul.products li.product.product-category h2 {
    color: white;
    margin: 0;
    padding: 20px;
    font-size: 18px;
}

ul.products li.product.product-category .mark {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

/* Smooth Transitions */
* {
    box-sizing: border-box;
}

.woocommerce ul.products li.product,
.woocommerce ul.products li.product img,
.woocommerce ul.products li.product .button {
    transition: all 0.3s ease;
}

/* Focus States for Accessibility */
.woocommerce ul.products li.product:focus-within {
    outline: 2px solid #ff6000;
    outline-offset: 2px;
}

.woocommerce .button:focus,
.woocommerce .add_to_cart_button:focus {
    outline: 2px solid #ff6000;
    outline-offset: 2px;
}

/* Loading States */
.woocommerce .blockUI.blockOverlay {
    background: rgba(255, 255, 255, 0.8) !important;
}

.woocommerce .loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6000;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
